#!/bin/bash

echo "🏠 Smart Home Dashboard Startup"
echo "==============================="

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js first."
    exit 1
fi

# Start the dashboard server in background
echo "🚀 Starting dashboard server..."
node dashboard-server.js &
SERVER_PID=$!

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 3

# Launch the dashboard in separate browser
echo "🌐 Launching dashboard in browser..."
node launch-dashboard.js

echo "==============================="
echo "✅ Smart Home Dashboard is running!"
echo "📡 Server PID: $SERVER_PID"
echo "🌐 Dashboard URL: http://localhost:8083"
echo ""
echo "To stop the server later, run:"
echo "kill $SERVER_PID"
echo ""
echo "Press Ctrl+C to stop the server now, or close this terminal."

# Keep the server running
wait $SERVER_PID
