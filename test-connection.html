<!DOCTYPE html>
<html>
<head>
    <title>Test Ecobee Connection</title>
</head>
<body>
    <h1>Testing Ecobee Connection</h1>
    <div id="status">Testing...</div>
    <div id="data"></div>
    
    <script>
    async function testConnection() {
        const statusDiv = document.getElementById('status');
        const dataDiv = document.getElementById('data');
        
        try {
            statusDiv.textContent = 'Connecting to Ecobee...';
            
            const url = 'http://*************/apps/api/19/devices/740?access_token=d044aa84-12f2-4384-b33e-e539e8724868';
            const proxyUrl = `/proxy?url=${encodeURIComponent(url)}`;
            
            console.log('Making request to:', proxyUrl);
            
            const response = await fetch(proxyUrl);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            statusDiv.textContent = '✅ Connected to Ecobee!';
            statusDiv.style.color = 'green';
            
            // Extract key data
            const attrs = {};
            data.attributes.forEach(attr => {
                attrs[attr.name] = attr.currentValue;
            });
            
            dataDiv.innerHTML = `
                <h2>Real Ecobee Data:</h2>
                <p><strong>Device:</strong> ${data.label}</p>
                <p><strong>Current Temperature:</strong> ${attrs.temperature}°F</p>
                <p><strong>Cooling Setpoint:</strong> ${attrs.coolingSetpoint}°F</p>
                <p><strong>Heating Setpoint:</strong> ${attrs.heatingSetpoint}°F</p>
                <p><strong>Mode:</strong> ${attrs.thermostatMode}</p>
                <p><strong>Operating State:</strong> ${attrs.thermostatOperatingState}</p>
                <p><strong>Humidity:</strong> ${attrs.humidity}%</p>
                <p><strong>Fan Mode:</strong> ${attrs.thermostatFanMode}</p>
            `;
            
            console.log('Success! Ecobee data:', data);
            
        } catch (error) {
            statusDiv.textContent = `❌ Connection failed: ${error.message}`;
            statusDiv.style.color = 'red';
            console.error('Connection test failed:', error);
        }
    }
    
    // Test connection when page loads
    testConnection();
    </script>
</body>
</html>
