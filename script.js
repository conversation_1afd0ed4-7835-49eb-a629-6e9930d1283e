// Smart Home Dashboard - Professional Implementation
class SmartHomeDashboard {
    constructor() {
        // Real Ecobee data properties
        this.currentTemp = 0;
        this.heatingSetpoint = 0;
        this.coolingSetpoint = 0;
        this.thermostatSetpoint = 0;
        this.humidity = 0;
        this.thermostatMode = 'auto';
        this.thermostatFanMode = 'auto';
        this.operatingState = 'idle';
        this.deviceStatus = 'offline';
        this.deviceAlive = false;
        this.temperatureUnit = 'F';

        // System limits
        this.minHeatingSetpoint = 45;
        this.maxHeatingSetpoint = 79;
        this.minCoolingSetpoint = 65;
        this.maxCoolingSetpoint = 92;

        // Supported capabilities
        this.supportedModes = [];
        this.supportedFanModes = [];

        // Connection status
        this.isConnected = false;
        this.lastUpdate = null;
        
        // API Configuration - Real Hubitat Integration
        this.config = {
            // Hubitat API - Real Configuration
            hubitatUrl: 'http://*************',
            hubitatToken: 'd044aa84-12f2-4384-b33e-e539e8724868',
            ecobeeDeviceId: '740',

            // Maker API endpoints
            makerApiPath: '/apps/api/19/devices',

            // Update intervals
            updateInterval: 10000, // 10 seconds for real data
            retryInterval: 5000     // 5 seconds for retries
        };
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.updateDateTime();
        this.startPeriodicUpdates();
        await this.discoverDevices();
        await this.loadThermostatData();
    }

    setupEventListeners() {
        // Main temperature controls
        document.getElementById('temp-up').addEventListener('click', () => {
            this.adjustMainTemperature(1);
        });

        document.getElementById('temp-down').addEventListener('click', () => {
            this.adjustMainTemperature(-1);
        });

        // Heating setpoint controls
        document.getElementById('heat-up').addEventListener('click', () => {
            this.adjustHeatingSetpoint(1);
        });

        document.getElementById('heat-down').addEventListener('click', () => {
            this.adjustHeatingSetpoint(-1);
        });

        // Cooling setpoint controls
        document.getElementById('cool-up').addEventListener('click', () => {
            this.adjustCoolingSetpoint(1);
        });

        document.getElementById('cool-down').addEventListener('click', () => {
            this.adjustCoolingSetpoint(-1);
        });

        // Mode buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setThermostatMode(e.target.dataset.mode);
            });
        });

        // Fan buttons
        document.querySelectorAll('.fan-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFanMode(e.target.dataset.fan);
            });
        });

        // Quick actions
        document.getElementById('away-mode').addEventListener('click', () => {
            this.setAwayMode();
        });

        document.getElementById('resume-program').addEventListener('click', () => {
            this.resumeProgram();
        });

        document.getElementById('refresh-data').addEventListener('click', () => {
            this.refreshData();
        });
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: true 
        };

        document.getElementById('current-date').textContent = 
            now.toLocaleDateString('en-US', dateOptions);
        document.getElementById('current-time').textContent = 
            now.toLocaleTimeString('en-US', timeOptions);
    }

    startPeriodicUpdates() {
        // Update time every minute
        setInterval(() => this.updateDateTime(), 60000);
        
        // Update thermostat data periodically
        setInterval(() => this.loadThermostatData(), this.config.updateInterval);
    }

    async discoverDevices() {
        this.updateConnectionStatus('Connecting to Hubitat C8...');

        try {
            console.log('Attempting to connect to Hubitat...');

            // Connect directly to your Hubitat Ecobee device
            const deviceData = await this.getHubitatDevice();
            if (deviceData && deviceData.attributes) {
                this.updateConnectionStatus('✅ Connected to Ecobee via Hubitat C8', true);
                this.processThermostatData(deviceData);
                this.showStatusMessage('🏠 Connected to Living Room thermostat', 'success');
                return;
            }

            throw new Error('No device data received from Hubitat');

        } catch (error) {
            console.error('Hubitat connection failed:', error);
            this.updateConnectionStatus(`❌ Connection Error: ${error.message}`, false);
            this.showStatusMessage(`Connection failed: ${error.message}`, 'error');

            // Retry connection with exponential backoff
            const retryDelay = Math.min(this.config.retryInterval * (this.retryCount || 1), 30000);
            this.retryCount = (this.retryCount || 0) + 1;

            console.log(`Retrying connection in ${retryDelay/1000} seconds... (attempt ${this.retryCount})`);
            setTimeout(() => {
                this.discoverDevices();
            }, retryDelay);
        }
    }

    async checkHomeAssistant() {
        if (!this.config.homeAssistantUrl || !this.config.homeAssistantToken) {
            return false;
        }
        
        try {
            const response = await fetch(`${this.config.homeAssistantUrl}/api/`, {
                headers: {
                    'Authorization': `Bearer ${this.config.homeAssistantToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                console.log('Home Assistant connection successful');
                return true;
            }
        } catch (error) {
            console.log('Home Assistant not available:', error.message);
        }
        
        return false;
    }

    async getHubitatDevice() {
        const url = `${this.config.hubitatUrl}${this.config.makerApiPath}/${this.config.ecobeeDeviceId}?access_token=${this.config.hubitatToken}`;

        console.log('Fetching Ecobee data from:', url);

        try {
            // Use a proxy approach to avoid CORS issues
            const response = await this.makeProxiedRequest(url);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const deviceData = await response.json();
            console.log('Ecobee device data received:', deviceData);

            return deviceData;

        } catch (error) {
            console.error('Failed to fetch Hubitat device:', error);
            throw error;
        }
    }

    async makeProxiedRequest(url) {
        // Always use the proxy server to avoid CORS issues
        console.log('Using proxy for request:', url);
        return await this.makeServerSideRequest(url);
    }

    async makeServerSideRequest(url) {
        // Use the proxy endpoint to bypass CORS
        const proxyUrl = `/proxy?url=${encodeURIComponent(url)}`;

        console.log('Making proxy request to:', proxyUrl);
        console.log('Original URL:', url);

        try {
            const response = await fetch(proxyUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 10000 // 10 second timeout
            });

            console.log('Proxy response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Proxy error response:', errorText);
                throw new Error(`Proxy request failed: ${response.status} ${response.statusText} - ${errorText}`);
            }

            return response;

        } catch (error) {
            console.error('Proxy request error:', error);
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('Network connection failed - check if server is running');
            }
            throw new Error(`Connection failed: ${error.message}`);
        }
    }

    async scanLocalNetwork() {
        this.updateConnectionStatus('Scanning local network...');
        
        // This would need proper implementation with CORS handling
        // For now, we simulate the scan
        console.log('Local network scan would run here');
    }

    processHubitatDevices(devices) {
        // Process Hubitat device list to find thermostats
        const thermostats = devices.filter(device => 
            device.type && device.type.toLowerCase().includes('thermostat')
        );
        
        if (thermostats.length > 0) {
            console.log('Found thermostats:', thermostats);
            this.loadHubitatThermostat(thermostats[0]);
        }
    }

    async loadHubitatThermostat(thermostat) {
        try {
            const response = await fetch(`${this.config.hubitatUrl}/device/${thermostat.id}`, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.processThermostatData(data);
            }
        } catch (error) {
            console.error('Failed to load Hubitat thermostat:', error);
        }
    }

    processThermostatData(data) {
        console.log('Processing complete thermostat data:', data);

        if (data.attributes) {
            // Convert attributes array to object for easier access
            const attrs = {};
            data.attributes.forEach(attr => {
                attrs[attr.name] = attr.currentValue;
            });

            // Temperature readings
            this.currentTemp = parseFloat(attrs.temperature) || 0;
            this.heatingSetpoint = parseFloat(attrs.heatingSetpoint) || 0;
            this.coolingSetpoint = parseFloat(attrs.coolingSetpoint) || 0;
            this.thermostatSetpoint = parseFloat(attrs.thermostatSetpoint) || 0;
            this.humidity = parseFloat(attrs.humidity) || 0;

            // System status
            this.thermostatMode = attrs.thermostatMode || 'auto';
            this.thermostatFanMode = attrs.thermostatFanMode || 'auto';
            this.operatingState = attrs.thermostatOperatingState || 'idle';
            this.deviceAlive = attrs.deviceAlive === 'true';
            this.deviceStatus = attrs['DeviceWatch-DeviceStatus'] || 'unknown';
            this.temperatureUnit = attrs.deviceTemperatureUnit || 'F';

            // System limits
            this.minHeatingSetpoint = parseFloat(attrs.minHeatingSetpoint) || 45;
            this.maxHeatingSetpoint = parseFloat(attrs.maxHeatingSetpoint) || 79;
            this.minCoolingSetpoint = parseFloat(attrs.minCoolingSetpoint) || 65;
            this.maxCoolingSetpoint = parseFloat(attrs.maxCoolingSetpoint) || 92;

            // Supported capabilities
            try {
                this.supportedModes = JSON.parse(attrs.supportedThermostatModes || '[]');
                this.supportedFanModes = JSON.parse(attrs.supportedThermostatFanModes || '[]');
            } catch (e) {
                console.warn('Failed to parse supported modes:', e);
                this.supportedModes = ['auto', 'heat', 'cool', 'off'];
                this.supportedFanModes = ['auto', 'on'];
            }

            console.log(`Complete Ecobee Data:
                Current: ${this.currentTemp}°${this.temperatureUnit}
                Heating: ${this.heatingSetpoint}°${this.temperatureUnit}
                Cooling: ${this.coolingSetpoint}°${this.temperatureUnit}
                Mode: ${this.thermostatMode}
                Fan: ${this.thermostatFanMode}
                State: ${this.operatingState}
                Humidity: ${this.humidity}%
                Status: ${this.deviceStatus}`);
        }

        this.updateAllDisplays();
        this.lastUpdate = new Date();
        this.isConnected = true;
    }

    loadDemoData() {
        // Load realistic demo data with live updates
        this.currentTemp = 71.5 + Math.random() * 1;
        this.targetTemp = 72;
        this.mode = 'auto';
        this.isConnected = false;
        
        // Simulate realistic temperature changes
        setInterval(() => {
            const targetDiff = this.targetTemp - this.currentTemp;
            const change = targetDiff * 0.1 + (Math.random() - 0.5) * 0.2;
            this.currentTemp += change;
            this.currentTemp = Math.round(this.currentTemp * 10) / 10;
            this.updateThermostatDisplay();
            this.updateStats();
        }, 5000);
        
        this.updateThermostatDisplay();
        this.updateStats();
        this.lastUpdate = new Date();
    }

    async loadThermostatData() {
        if (this.isConnected) {
            try {
                // Refresh real data from Hubitat
                const deviceData = await this.getHubitatDevice();
                if (deviceData) {
                    this.processThermostatData(deviceData);
                }
            } catch (error) {
                console.error('Failed to refresh thermostat data:', error);
                this.updateConnectionStatus('Connection lost - Retrying...', false);
                this.isConnected = false;
            }
        }

        document.getElementById('last-update').textContent =
            new Date().toLocaleTimeString();
    }

    updateConnectionStatus(message, connected = false) {
        const statusElement = document.getElementById('connection-status');
        const dot = statusElement.querySelector('.status-dot');
        const text = statusElement.querySelector('span');
        
        text.textContent = message;
        dot.style.background = connected ? '#10b981' : '#f59e0b';
        
        document.getElementById('api-status').textContent = 
            connected ? 'Connected' : 'Demo Mode';
        document.getElementById('network-status').textContent = 
            connected ? 'Live Data' : 'Simulated';
    }

    updateAllDisplays() {
        this.updateThermostatDisplay();
        this.updateStatsCards();
        this.updateSystemInfo();
        this.updateControlStates();
    }

    updateThermostatDisplay() {
        // Main temperature display
        document.getElementById('thermostat-temp').textContent =
            Math.round(this.currentTemp);

        // Target temperature (use appropriate setpoint based on mode)
        let targetTemp = this.thermostatSetpoint;
        if (this.thermostatMode === 'cool') {
            targetTemp = this.coolingSetpoint;
        } else if (this.thermostatMode === 'heat') {
            targetTemp = this.heatingSetpoint;
        }

        document.getElementById('target-temp').textContent = targetTemp;

        // Individual setpoints
        document.getElementById('heating-setpoint').textContent =
            `${this.heatingSetpoint}°${this.temperatureUnit}`;
        document.getElementById('cooling-setpoint').textContent =
            `${this.coolingSetpoint}°${this.temperatureUnit}`;
    }

    updateStatsCards() {
        // Current temperature
        document.getElementById('current-temp').textContent =
            `${this.currentTemp}°${this.temperatureUnit}`;

        // Humidity
        document.getElementById('humidity-value').textContent = `${this.humidity}%`;

        // Operating state with color coding
        const operatingElement = document.getElementById('operating-state');
        const operatingStatus = this.operatingState.charAt(0).toUpperCase() + this.operatingState.slice(1);
        operatingElement.textContent = operatingStatus;

        // Fan mode
        document.getElementById('fan-mode').textContent =
            this.thermostatFanMode.charAt(0).toUpperCase() + this.thermostatFanMode.slice(1);

        // Enhanced status indicators with real-time feedback
        const currentTarget = this.thermostatMode === 'cool' ? this.coolingSetpoint :
                             this.thermostatMode === 'heat' ? this.heatingSetpoint :
                             this.thermostatSetpoint;

        const tempDiff = this.currentTemp - currentTarget;
        const tempStatusElement = document.getElementById('temp-status');
        if (Math.abs(tempDiff) < 0.5) {
            tempStatusElement.textContent = '✓ At target temperature';
            tempStatusElement.className = 'stat-change positive';
        } else if (tempDiff > 0) {
            tempStatusElement.textContent = `+${tempDiff.toFixed(1)}° above target`;
            tempStatusElement.className = 'stat-change negative';
        } else {
            tempStatusElement.textContent = `${tempDiff.toFixed(1)}° below target`;
            tempStatusElement.className = 'stat-change negative';
        }

        // Humidity status with color coding
        const humidityStatusElement = document.getElementById('humidity-status');
        if (this.humidity > 65) {
            humidityStatusElement.textContent = 'High humidity';
            humidityStatusElement.className = 'stat-change negative';
        } else if (this.humidity < 30) {
            humidityStatusElement.textContent = 'Low humidity';
            humidityStatusElement.className = 'stat-change negative';
        } else {
            humidityStatusElement.textContent = 'Optimal range';
            humidityStatusElement.className = 'stat-change positive';
        }

        // Operating status with detailed feedback
        const operatingStatusElement = document.getElementById('operating-status');
        if (this.operatingState === 'cooling') {
            operatingStatusElement.textContent = '❄️ Cooling active';
            operatingStatusElement.className = 'stat-change positive';
        } else if (this.operatingState === 'heating') {
            operatingStatusElement.textContent = '🔥 Heating active';
            operatingStatusElement.className = 'stat-change positive';
        } else if (this.operatingState === 'idle') {
            operatingStatusElement.textContent = '⏸️ System idle';
            operatingStatusElement.className = 'stat-change neutral';
        } else {
            operatingStatusElement.textContent = `System ${this.operatingState}`;
            operatingStatusElement.className = 'stat-change neutral';
        }

        // Fan status
        const fanStatusElement = document.getElementById('fan-status');
        if (this.thermostatFanMode === 'auto') {
            fanStatusElement.textContent = '🔄 Automatic control';
            fanStatusElement.className = 'stat-change positive';
        } else if (this.thermostatFanMode === 'on') {
            fanStatusElement.textContent = '💨 Running continuously';
            fanStatusElement.className = 'stat-change neutral';
        } else {
            fanStatusElement.textContent = '🌀 Circulating';
            fanStatusElement.className = 'stat-change neutral';
        }
    }

    updateControlStates() {
        // Update mode buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.mode === this.thermostatMode);
        });

        // Update fan buttons
        document.querySelectorAll('.fan-btn').forEach(btn => {
            const fanMode = btn.dataset.fan === 'circulate' ? 'circulate' :
                           btn.dataset.fan === 'on' ? 'on' : 'auto';
            btn.classList.toggle('active', fanMode === this.thermostatFanMode);
        });
    }

    updateSystemInfo() {
        // Connection status
        document.getElementById('api-status').textContent =
            this.isConnected ? 'Connected' : 'Disconnected';
        document.getElementById('device-status').textContent =
            this.deviceAlive ? this.deviceStatus : 'Offline';
        document.getElementById('last-update').textContent =
            this.lastUpdate ? this.lastUpdate.toLocaleTimeString() : '--';

        // Temperature limits
        document.getElementById('min-heating').textContent =
            `${this.minHeatingSetpoint}°${this.temperatureUnit}`;
        document.getElementById('max-heating').textContent =
            `${this.maxHeatingSetpoint}°${this.temperatureUnit}`;
        document.getElementById('min-cooling').textContent =
            `${this.minCoolingSetpoint}°${this.temperatureUnit}`;
        document.getElementById('max-cooling').textContent =
            `${this.maxCoolingSetpoint}°${this.temperatureUnit}`;

        // Capabilities
        document.getElementById('supported-modes').textContent =
            this.supportedModes.join(', ');
        document.getElementById('supported-fan-modes').textContent =
            this.supportedFanModes.join(', ');
        document.getElementById('temp-unit').textContent =
            this.temperatureUnit === 'F' ? 'Fahrenheit' : 'Celsius';
    }

    // Temperature control functions
    async adjustMainTemperature(delta) {
        // Adjust the appropriate setpoint based on current mode
        if (this.thermostatMode === 'cool') {
            await this.adjustCoolingSetpoint(delta);
        } else if (this.thermostatMode === 'heat') {
            await this.adjustHeatingSetpoint(delta);
        } else if (this.thermostatMode === 'auto') {
            // In auto mode, adjust cooling setpoint
            await this.adjustCoolingSetpoint(delta);
        }
    }

    async adjustHeatingSetpoint(delta) {
        const newTemp = this.heatingSetpoint + delta;
        if (newTemp >= this.minHeatingSetpoint && newTemp <= this.maxHeatingSetpoint) {
            // Immediately update the display for instant feedback
            this.heatingSetpoint = newTemp;
            this.updateAllDisplays();
            this.showStatusMessage(`Heating setpoint changed to ${newTemp}°${this.temperatureUnit}`, 'success');

            try {
                await this.sendHubitatCommand('setHeatingSetpoint', newTemp);
                console.log(`Heating setpoint command sent: ${newTemp}°${this.temperatureUnit}`);
                this.showStatusMessage(`✓ Heating setpoint updated to ${newTemp}°${this.temperatureUnit}`, 'success');
            } catch (error) {
                console.error('Failed to set heating setpoint:', error);
                this.showStatusMessage(`✗ Failed to update heating setpoint`, 'error');
                // Revert the change on error
                this.heatingSetpoint = this.heatingSetpoint - delta;
                this.updateAllDisplays();
            }
        } else {
            this.showStatusMessage(`Heating setpoint must be between ${this.minHeatingSetpoint}°-${this.maxHeatingSetpoint}°${this.temperatureUnit}`, 'warning');
        }
    }

    async adjustCoolingSetpoint(delta) {
        const newTemp = this.coolingSetpoint + delta;
        if (newTemp >= this.minCoolingSetpoint && newTemp <= this.maxCoolingSetpoint) {
            // Immediately update the display for instant feedback
            this.coolingSetpoint = newTemp;
            this.updateAllDisplays();
            this.showStatusMessage(`Cooling setpoint changed to ${newTemp}°${this.temperatureUnit}`, 'success');

            try {
                await this.sendHubitatCommand('setCoolingSetpoint', newTemp);
                console.log(`Cooling setpoint command sent: ${newTemp}°${this.temperatureUnit}`);
                this.showStatusMessage(`✓ Cooling setpoint updated to ${newTemp}°${this.temperatureUnit}`, 'success');
            } catch (error) {
                console.error('Failed to set cooling setpoint:', error);
                this.showStatusMessage(`✗ Failed to update cooling setpoint`, 'error');
                // Revert the change on error
                this.coolingSetpoint = this.coolingSetpoint - delta;
                this.updateAllDisplays();
            }
        } else {
            this.showStatusMessage(`Cooling setpoint must be between ${this.minCoolingSetpoint}°-${this.maxCoolingSetpoint}°${this.temperatureUnit}`, 'warning');
        }
    }

    async setThermostatMode(mode) {
        if (this.supportedModes.includes(mode)) {
            // Immediately update the display for instant feedback
            const oldMode = this.thermostatMode;
            this.thermostatMode = mode;
            this.updateAllDisplays();
            this.showStatusMessage(`Mode changed to ${mode.toUpperCase()}`, 'success');

            try {
                if (mode === 'emergencyHeat') {
                    await this.sendHubitatCommand('emergencyHeat', '');
                } else {
                    await this.sendHubitatCommand(mode, '');
                }
                console.log(`Thermostat mode command sent: ${mode}`);
                this.showStatusMessage(`✓ Thermostat mode set to ${mode.toUpperCase()}`, 'success');
            } catch (error) {
                console.error('Failed to set thermostat mode:', error);
                this.showStatusMessage(`✗ Failed to change mode to ${mode.toUpperCase()}`, 'error');
                // Revert the change on error
                this.thermostatMode = oldMode;
                this.updateAllDisplays();
            }
        } else {
            this.showStatusMessage(`Mode ${mode.toUpperCase()} is not supported`, 'warning');
        }
    }

    async setFanMode(fanMode) {
        // Immediately update the display for instant feedback
        const oldFanMode = this.thermostatFanMode;
        this.thermostatFanMode = fanMode;
        this.updateAllDisplays();
        this.showStatusMessage(`Fan mode changed to ${fanMode.toUpperCase()}`, 'success');

        try {
            if (fanMode === 'auto') {
                await this.sendHubitatCommand('fanAuto', '');
            } else if (fanMode === 'on') {
                await this.sendHubitatCommand('fanOn', '');
            } else if (fanMode === 'circulate') {
                await this.sendHubitatCommand('fanCirculate', '');
            }
            console.log(`Fan mode command sent: ${fanMode}`);
            this.showStatusMessage(`✓ Fan mode set to ${fanMode.toUpperCase()}`, 'success');
        } catch (error) {
            console.error('Failed to set fan mode:', error);
            this.showStatusMessage(`✗ Failed to change fan mode to ${fanMode.toUpperCase()}`, 'error');
            // Revert the change on error
            this.thermostatFanMode = oldFanMode;
            this.updateAllDisplays();
        }
    }

    async setAwayMode() {
        try {
            await this.sendHubitatCommand('setAway', '');
            console.log('Away mode activated');
        } catch (error) {
            console.error('Failed to set away mode:', error);
        }
    }

    async resumeProgram() {
        try {
            await this.sendHubitatCommand('resumeProgram', '');
            console.log('Program resumed');
        } catch (error) {
            console.error('Failed to resume program:', error);
        }
    }

    async refreshData() {
        try {
            await this.sendHubitatCommand('refresh', '');
            console.log('Data refresh requested');
            // Reload data after refresh
            setTimeout(() => this.loadThermostatData(), 2000);
        } catch (error) {
            console.error('Failed to refresh data:', error);
        }
    }

    // Status message system for immediate user feedback
    showStatusMessage(message, type = 'info') {
        // Create or update status message element
        let statusElement = document.getElementById('status-message');
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.id = 'status-message';
            statusElement.className = 'status-message';
            document.querySelector('.main-content').appendChild(statusElement);
        }

        // Set message and type
        statusElement.textContent = message;
        statusElement.className = `status-message ${type}`;
        statusElement.style.display = 'block';

        // Auto-hide after 3 seconds
        clearTimeout(this.statusTimeout);
        this.statusTimeout = setTimeout(() => {
            statusElement.style.display = 'none';
        }, 3000);
    }

    // Enhanced data refresh with immediate feedback
    async loadThermostatData() {
        if (this.isConnected) {
            try {
                const deviceData = await this.getHubitatDevice();
                if (deviceData) {
                    this.processThermostatData(deviceData);
                    // Show subtle update indicator
                    this.showStatusMessage('📡 Data refreshed', 'info');
                }
            } catch (error) {
                console.error('Failed to refresh thermostat data:', error);
                this.updateConnectionStatus('Connection lost - Retrying...', false);
                this.isConnected = false;
                this.showStatusMessage('⚠️ Connection lost, retrying...', 'warning');
            }
        }

        document.getElementById('last-update').textContent =
            new Date().toLocaleTimeString();
    }

    async sendThermostatCommand(command, params) {
        try {
            // Try different API endpoints based on available connections
            if (this.config.homeAssistantToken) {
                await this.sendHomeAssistantCommand(command, params);
            } else if (this.config.hubitatUrl) {
                await this.sendHubitatCommand(command, params);
            }
        } catch (error) {
            console.error('Failed to send thermostat command:', error);
        }
    }

    async sendHomeAssistantCommand(command, params) {
        const response = await fetch(`${this.config.homeAssistantUrl}/api/services/climate/set_temperature`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.homeAssistantToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                entity_id: 'climate.ecobee_thermostat',
                temperature: params.temperature
            })
        });
        
        if (!response.ok) {
            throw new Error('Home Assistant command failed');
        }
    }

    async sendHubitatCommand(command, value) {
        const url = `${this.config.hubitatUrl}${this.config.makerApiPath}/${this.config.ecobeeDeviceId}/${command}/${value}?access_token=${this.config.hubitatToken}`;

        console.log('Sending Hubitat command:', url);

        try {
            const response = await this.makeServerSideRequest(url);

            if (!response.ok) {
                throw new Error(`Hubitat command failed: HTTP ${response.status}`);
            }

            const result = await response.json();
            console.log('Hubitat command result:', result);

            // Refresh device data after command to get real updated values
            setTimeout(async () => {
                try {
                    const deviceData = await this.getHubitatDevice();
                    if (deviceData) {
                        this.processThermostatData(deviceData);
                        this.showStatusMessage('📡 Settings confirmed by thermostat', 'success');
                    }
                } catch (error) {
                    console.error('Failed to refresh after command:', error);
                }
            }, 2000);

            return result;

        } catch (error) {
            console.error('Hubitat command error:', error);
            throw error;
        }
    }
}

// Initialize the dashboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new SmartHomeDashboard();
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartHomeDashboard;
}
