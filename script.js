// Smart Home Dashboard - Professional Implementation
class SmartHomeDashboard {
    constructor() {
        this.currentTemp = 72;
        this.targetTemp = 72;
        this.mode = 'auto';
        this.isConnected = false;
        this.lastUpdate = null;
        
        // API Configuration - Real Hubitat Integration
        this.config = {
            // Hubitat API - Real Configuration
            hubitatUrl: 'http://*************',
            hubitatToken: 'd044aa84-12f2-4384-b33e-e539e8724868',
            ecobeeDeviceId: '740',

            // Maker API endpoints
            makerApiPath: '/apps/api/19/devices',

            // Update intervals
            updateInterval: 10000, // 10 seconds for real data
            retryInterval: 5000     // 5 seconds for retries
        };
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.updateDateTime();
        this.startPeriodicUpdates();
        await this.discoverDevices();
        await this.loadThermostatData();
    }

    setupEventListeners() {
        // Temperature controls
        document.getElementById('temp-up').addEventListener('click', () => {
            this.adjustTemperature(1);
        });
        
        document.getElementById('temp-down').addEventListener('click', () => {
            this.adjustTemperature(-1);
        });

        // Mode buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setMode(e.target.dataset.mode);
            });
        });

        // Schedule selector
        document.getElementById('schedule-select').addEventListener('change', (e) => {
            this.setSchedule(e.target.value);
        });

        // Quick actions
        document.getElementById('away-mode').addEventListener('click', () => {
            this.setAwayMode();
        });
        
        document.getElementById('sleep-mode').addEventListener('click', () => {
            this.setSleepMode();
        });
        
        document.getElementById('eco-mode').addEventListener('click', () => {
            this.setEcoMode();
        });
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: true 
        };

        document.getElementById('current-date').textContent = 
            now.toLocaleDateString('en-US', dateOptions);
        document.getElementById('current-time').textContent = 
            now.toLocaleTimeString('en-US', timeOptions);
    }

    startPeriodicUpdates() {
        // Update time every minute
        setInterval(() => this.updateDateTime(), 60000);
        
        // Update thermostat data periodically
        setInterval(() => this.loadThermostatData(), this.config.updateInterval);
    }

    async discoverDevices() {
        this.updateConnectionStatus('Connecting to Hubitat C8...');

        try {
            // Connect directly to your Hubitat Ecobee device
            const deviceData = await this.getHubitatDevice();
            if (deviceData) {
                this.updateConnectionStatus('Connected to Ecobee via Hubitat C8', true);
                this.processThermostatData(deviceData);
                return;
            }

            throw new Error('Failed to connect to Hubitat device');

        } catch (error) {
            console.error('Hubitat connection failed:', error);
            this.updateConnectionStatus(`Connection Error: ${error.message}`, false);

            // Retry connection
            setTimeout(() => {
                console.log('Retrying Hubitat connection...');
                this.discoverDevices();
            }, this.config.retryInterval);
        }
    }

    async checkHomeAssistant() {
        if (!this.config.homeAssistantUrl || !this.config.homeAssistantToken) {
            return false;
        }
        
        try {
            const response = await fetch(`${this.config.homeAssistantUrl}/api/`, {
                headers: {
                    'Authorization': `Bearer ${this.config.homeAssistantToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                console.log('Home Assistant connection successful');
                return true;
            }
        } catch (error) {
            console.log('Home Assistant not available:', error.message);
        }
        
        return false;
    }

    async getHubitatDevice() {
        const url = `${this.config.hubitatUrl}${this.config.makerApiPath}/${this.config.ecobeeDeviceId}?access_token=${this.config.hubitatToken}`;

        console.log('Fetching Ecobee data from:', url);

        try {
            // Use a proxy approach to avoid CORS issues
            const response = await this.makeProxiedRequest(url);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const deviceData = await response.json();
            console.log('Ecobee device data received:', deviceData);

            return deviceData;

        } catch (error) {
            console.error('Failed to fetch Hubitat device:', error);
            throw error;
        }
    }

    async makeProxiedRequest(url) {
        // Try direct request first (in case CORS is configured)
        try {
            const response = await fetch(url, {
                method: 'GET',
                mode: 'no-cors'
            });

            // no-cors mode doesn't give us access to response data
            // So we need a different approach
            throw new Error('CORS blocked');

        } catch (error) {
            // Use a server-side proxy or JSONP approach
            return await this.makeServerSideRequest(url);
        }
    }

    async makeServerSideRequest(url) {
        // Use the proxy endpoint to bypass CORS
        const proxyUrl = `/proxy?url=${encodeURIComponent(url)}`;

        console.log('Using proxy:', proxyUrl);

        try {
            const response = await fetch(proxyUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`Proxy request failed: ${response.status} ${response.statusText}`);
            }

            return response;

        } catch (error) {
            console.error('Proxy request error:', error);
            throw new Error(`Connection failed: ${error.message}`);
        }
    }

    async scanLocalNetwork() {
        this.updateConnectionStatus('Scanning local network...');
        
        // This would need proper implementation with CORS handling
        // For now, we simulate the scan
        console.log('Local network scan would run here');
    }

    processHubitatDevices(devices) {
        // Process Hubitat device list to find thermostats
        const thermostats = devices.filter(device => 
            device.type && device.type.toLowerCase().includes('thermostat')
        );
        
        if (thermostats.length > 0) {
            console.log('Found thermostats:', thermostats);
            this.loadHubitatThermostat(thermostats[0]);
        }
    }

    async loadHubitatThermostat(thermostat) {
        try {
            const response = await fetch(`${this.config.hubitatUrl}/device/${thermostat.id}`, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.processThermostatData(data);
            }
        } catch (error) {
            console.error('Failed to load Hubitat thermostat:', error);
        }
    }

    processThermostatData(data) {
        console.log('Processing thermostat data:', data);

        // Extract real data from Hubitat device response
        if (data.attributes) {
            // Convert attributes array to object for easier access
            const attrs = {};
            data.attributes.forEach(attr => {
                attrs[attr.name] = attr.currentValue;
            });

            // Current temperature
            if (attrs.temperature !== undefined) {
                this.currentTemp = parseFloat(attrs.temperature);
            }

            // Target temperature - use thermostatSetpoint or appropriate setpoint
            if (attrs.thermostatSetpoint !== undefined) {
                this.targetTemp = parseFloat(attrs.thermostatSetpoint);
            } else if (attrs.coolingSetpoint !== undefined && (attrs.thermostatMode === 'cool' || attrs.thermostatMode === 'auto')) {
                this.targetTemp = parseFloat(attrs.coolingSetpoint);
            } else if (attrs.heatingSetpoint !== undefined) {
                this.targetTemp = parseFloat(attrs.heatingSetpoint);
            }

            // Thermostat mode
            if (attrs.thermostatMode) {
                this.mode = attrs.thermostatMode.toLowerCase();
            }

            // Operating state
            if (attrs.thermostatOperatingState) {
                this.operatingState = attrs.thermostatOperatingState;
            }

            // Humidity
            if (attrs.humidity !== undefined) {
                this.humidity = parseFloat(attrs.humidity);
            }

            console.log(`Real Ecobee Data - Current: ${this.currentTemp}°F, Target: ${this.targetTemp}°F, Mode: ${this.mode}, State: ${this.operatingState}`);
        }

        this.updateThermostatDisplay();
        this.updateStats();
        this.lastUpdate = new Date();
        this.isConnected = true;
    }

    loadDemoData() {
        // Load realistic demo data with live updates
        this.currentTemp = 71.5 + Math.random() * 1;
        this.targetTemp = 72;
        this.mode = 'auto';
        this.isConnected = false;
        
        // Simulate realistic temperature changes
        setInterval(() => {
            const targetDiff = this.targetTemp - this.currentTemp;
            const change = targetDiff * 0.1 + (Math.random() - 0.5) * 0.2;
            this.currentTemp += change;
            this.currentTemp = Math.round(this.currentTemp * 10) / 10;
            this.updateThermostatDisplay();
            this.updateStats();
        }, 5000);
        
        this.updateThermostatDisplay();
        this.updateStats();
        this.lastUpdate = new Date();
    }

    async loadThermostatData() {
        if (this.isConnected) {
            try {
                // Refresh real data from Hubitat
                const deviceData = await this.getHubitatDevice();
                if (deviceData) {
                    this.processThermostatData(deviceData);
                }
            } catch (error) {
                console.error('Failed to refresh thermostat data:', error);
                this.updateConnectionStatus('Connection lost - Retrying...', false);
                this.isConnected = false;
            }
        }

        document.getElementById('last-update').textContent =
            new Date().toLocaleTimeString();
    }

    updateConnectionStatus(message, connected = false) {
        const statusElement = document.getElementById('connection-status');
        const dot = statusElement.querySelector('.status-dot');
        const text = statusElement.querySelector('span');
        
        text.textContent = message;
        dot.style.background = connected ? '#10b981' : '#f59e0b';
        
        document.getElementById('api-status').textContent = 
            connected ? 'Connected' : 'Demo Mode';
        document.getElementById('network-status').textContent = 
            connected ? 'Live Data' : 'Simulated';
    }

    updateThermostatDisplay() {
        document.getElementById('thermostat-temp').textContent = 
            Math.round(this.currentTemp);
        document.getElementById('target-temp').textContent = 
            this.targetTemp;
        document.getElementById('current-temp').textContent = 
            `${Math.round(this.currentTemp)}°F`;
        
        // Update mode buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.mode === this.mode);
        });
    }

    updateStats() {
        // Update stat cards with realistic data
        const energyUsage = (2.1 + Math.random() * 0.8).toFixed(1);
        document.getElementById('energy-usage').textContent = `${energyUsage}kWh`;
        document.getElementById('security-status').textContent = 'Armed';
        document.getElementById('device-count').textContent = '8';
        
        // Update change indicators with realistic values
        const tempDiff = (this.currentTemp - this.targetTemp).toFixed(1);
        document.getElementById('temp-change').textContent = 
            `${tempDiff > 0 ? '+' : ''}${tempDiff}°F from target`;
        document.getElementById('energy-change').textContent = '-12% from yesterday';
        document.getElementById('security-change').textContent = 'All systems secure';
        document.getElementById('device-change').textContent = '+1 device today';
    }

    async adjustTemperature(delta) {
        const newTemp = this.targetTemp + delta;
        if (newTemp >= 50 && newTemp <= 90) {
            this.targetTemp = newTemp;

            if (this.isConnected) {
                try {
                    // Use the appropriate setpoint based on current mode
                    if (this.mode === 'cool' || (this.mode === 'auto' && this.operatingState === 'cooling')) {
                        await this.sendHubitatCommand('setCoolingSetpoint', newTemp);
                    } else if (this.mode === 'heat' || (this.mode === 'auto' && this.operatingState === 'heating')) {
                        await this.sendHubitatCommand('setHeatingSetpoint', newTemp);
                    } else {
                        // Default to cooling setpoint for auto mode
                        await this.sendHubitatCommand('setCoolingSetpoint', newTemp);
                    }
                    console.log(`Temperature command sent: ${newTemp}°F`);
                } catch (error) {
                    console.error('Failed to set temperature:', error);
                }
            }

            this.updateThermostatDisplay();
            console.log(`Temperature adjusted to ${newTemp}°F`);
        }
    }

    async setMode(mode) {
        this.mode = mode;

        if (this.isConnected) {
            try {
                // Use the direct mode commands available
                if (mode === 'auto') {
                    await this.sendHubitatCommand('auto', '');
                } else if (mode === 'cool') {
                    await this.sendHubitatCommand('cool', '');
                } else if (mode === 'heat') {
                    await this.sendHubitatCommand('heat', '');
                } else if (mode === 'off') {
                    await this.sendHubitatCommand('off', '');
                }
                console.log(`Mode command sent: ${mode}`);
            } catch (error) {
                console.error('Failed to set mode:', error);
            }
        }

        this.updateThermostatDisplay();
        console.log(`Mode changed to ${mode}`);
    }

    async setSchedule(schedule) {
        const scheduleTemps = {
            home: 72,
            away: 68,
            sleep: 70,
            custom: this.targetTemp
        };
        
        if (scheduleTemps[schedule]) {
            this.targetTemp = scheduleTemps[schedule];
            
            if (this.isConnected) {
                await this.sendThermostatCommand('setSchedule', { 
                    schedule: schedule,
                    temperature: this.targetTemp 
                });
            }
            
            this.updateThermostatDisplay();
            console.log(`Schedule changed to ${schedule} (${this.targetTemp}°F)`);
        }
    }

    async setAwayMode() {
        this.targetTemp = 68;
        this.mode = 'auto';
        
        if (this.isConnected) {
            await this.sendThermostatCommand('setAway', { temperature: 68 });
        }
        
        document.getElementById('schedule-select').value = 'away';
        this.updateThermostatDisplay();
        console.log('Away mode activated - Temperature set to 68°F');
    }

    async setSleepMode() {
        this.targetTemp = 70;
        
        if (this.isConnected) {
            await this.sendThermostatCommand('setSleep', { temperature: 70 });
        }
        
        document.getElementById('schedule-select').value = 'sleep';
        this.updateThermostatDisplay();
        console.log('Sleep mode activated - Temperature set to 70°F');
    }

    async setEcoMode() {
        // Implement eco mode logic
        if (this.isConnected) {
            await this.sendThermostatCommand('setEco', { enabled: true });
        }
        
        console.log('Eco mode activated - Energy saving enabled');
    }

    async sendThermostatCommand(command, params) {
        try {
            // Try different API endpoints based on available connections
            if (this.config.homeAssistantToken) {
                await this.sendHomeAssistantCommand(command, params);
            } else if (this.config.hubitatUrl) {
                await this.sendHubitatCommand(command, params);
            }
        } catch (error) {
            console.error('Failed to send thermostat command:', error);
        }
    }

    async sendHomeAssistantCommand(command, params) {
        const response = await fetch(`${this.config.homeAssistantUrl}/api/services/climate/set_temperature`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.homeAssistantToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                entity_id: 'climate.ecobee_thermostat',
                temperature: params.temperature
            })
        });
        
        if (!response.ok) {
            throw new Error('Home Assistant command failed');
        }
    }

    async sendHubitatCommand(command, value) {
        const url = `${this.config.hubitatUrl}${this.config.makerApiPath}/${this.config.ecobeeDeviceId}/${command}/${value}?access_token=${this.config.hubitatToken}`;

        console.log('Sending Hubitat command:', url);

        try {
            const response = await this.makeServerSideRequest(url);

            if (!response.ok) {
                throw new Error(`Hubitat command failed: HTTP ${response.status}`);
            }

            const result = await response.json();
            console.log('Hubitat command result:', result);

            // Refresh device data after command
            setTimeout(() => this.loadThermostatData(), 2000);

            return result;

        } catch (error) {
            console.error('Hubitat command error:', error);
            throw error;
        }
    }
}

// Initialize the dashboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new SmartHomeDashboard();
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartHomeDashboard;
}
