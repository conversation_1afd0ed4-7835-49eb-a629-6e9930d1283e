/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: #f8fafc;
    color: #1e293b;
    line-height: 1.6;
}

/* Dashboard Layout */
.dashboard {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    color: white;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.nav-menu {
    flex: 1;
    padding: 2rem 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: white;
}

.nav-item.active {
    background: rgba(59, 130, 246, 0.1);
    color: #60a5fa;
    border-left-color: #3b82f6;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.avatar {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info .name {
    font-weight: 500;
    font-size: 0.875rem;
}

.user-info .status {
    font-size: 0.75rem;
    color: #94a3b8;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    background: #f8fafc;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 0.25rem;
}

.header p {
    color: #64748b;
    font-size: 1rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.datetime {
    text-align: right;
}

.date {
    font-size: 0.875rem;
    color: #64748b;
}

.time {
    font-size: 1.125rem;
    font-weight: 600;
    color: #0f172a;
}

.notification-btn {
    width: 44px;
    height: 44px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #64748b;
}

.notification-btn:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Stat Cards */
.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.stat-icon.temperature { background: linear-gradient(135deg, #f59e0b, #d97706); }
.stat-icon.energy { background: linear-gradient(135deg, #10b981, #059669); }
.stat-icon.security { background: linear-gradient(135deg, #ef4444, #dc2626); }
.stat-icon.devices { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.875rem;
    font-weight: 700;
    color: #0f172a;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0.25rem 0;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
}

.stat-change.positive {
    background: #dcfce7;
    color: #166534;
}

.stat-change.negative {
    background: #fef2f2;
    color: #991b1b;
}

.stat-change.neutral {
    background: #f1f5f9;
    color: #475569;
}

/* Thermostat Card */
.thermostat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.card-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f172a;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #64748b;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.thermostat-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.temperature-circle {
    width: 200px;
    height: 200px;
    border: 8px solid #e2e8f0;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 auto;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.current-temp {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
}

.temp-value {
    font-size: 3rem;
    font-weight: 700;
    color: #0f172a;
}

.temp-unit {
    font-size: 1.5rem;
    color: #64748b;
}

.target-temp {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 0.5rem;
}

.temp-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.temp-btn {
    width: 48px;
    height: 48px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #64748b;
}

.temp-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: scale(1.05);
}

.thermostat-controls {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.control-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.mode-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.mode-btn {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #64748b;
}

.mode-btn:hover {
    border-color: #cbd5e1;
}

.mode-btn.active {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
}

.schedule-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    font-size: 0.875rem;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.schedule-select:focus {
    outline: none;
    border-color: #3b82f6;
}

.quick-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #475569;
}

.action-btn:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

/* System Card */
.system-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    margin-top: 2rem;
}

.system-card .card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #0f172a;
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    font-weight: 500;
    color: #64748b;
}

.info-item .value {
    font-weight: 600;
    color: #0f172a;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .thermostat-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .temperature-circle {
        width: 160px;
        height: 160px;
    }
    
    .temp-value {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .mode-buttons {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        flex-direction: column;
    }
}
