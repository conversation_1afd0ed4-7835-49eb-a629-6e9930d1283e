const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8083;

// Hubitat configuration
const HUBITAT_CONFIG = {
    url: 'http://*************',
    apiPath: '/apps/api/19/devices',
    deviceId: '740',
    token: 'd044aa84-12f2-4384-b33e-e539e8724868'
};

// Get real thermostat data from Hubitat
async function getThermostatData() {
    return new Promise((resolve, reject) => {
        const url = `${HUBITAT_CONFIG.url}${HUBITAT_CONFIG.apiPath}/${HUBITAT_CONFIG.deviceId}?access_token=${HUBITAT_CONFIG.token}`;
        
        console.log('Fetching thermostat data from:', url);
        
        http.get(url, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const deviceData = JSON.parse(data);
                    
                    // Extract thermostat attributes
                    const attrs = {};
                    deviceData.attributes.forEach(attr => {
                        attrs[attr.name] = attr.currentValue;
                    });
                    
                    const thermostatData = {
                        name: deviceData.label,
                        currentTemp: parseFloat(attrs.temperature) || 0,
                        heatingSetpoint: parseFloat(attrs.heatingSetpoint) || 0,
                        coolingSetpoint: parseFloat(attrs.coolingSetpoint) || 0,
                        humidity: parseFloat(attrs.humidity) || 0,
                        mode: attrs.thermostatMode || 'auto',
                        fanMode: attrs.thermostatFanMode || 'auto',
                        operatingState: attrs.thermostatOperatingState || 'idle',
                        lastUpdate: new Date().toLocaleString()
                    };
                    
                    console.log('Thermostat data retrieved:', thermostatData);
                    resolve(thermostatData);
                } catch (error) {
                    reject(error);
                }
            });
        }).on('error', reject);
    });
}

// Send command to thermostat
async function sendThermostatCommand(command, value = '') {
    return new Promise((resolve, reject) => {
        const url = `${HUBITAT_CONFIG.url}${HUBITAT_CONFIG.apiPath}/${HUBITAT_CONFIG.deviceId}/${command}/${value}?access_token=${HUBITAT_CONFIG.token}`;
        
        console.log('Sending command:', url);
        
        http.get(url, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log('Command sent successfully');
                resolve(data);
            });
        }).on('error', reject);
    });
}

// Generate dashboard HTML with real data
function generateDashboardHTML(thermostatData) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Home Dashboard - ${thermostatData.name}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .temp-display {
            text-align: center;
            margin: 20px 0;
        }
        .current-temp {
            font-size: 4rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }
        .temp-label {
            color: #718096;
            font-size: 1.1rem;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .control-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        
        .btn-primary {
            background: #4299e1;
            color: white;
        }
        .btn-primary:hover {
            background: #3182ce;
            transform: scale(1.05);
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        .btn-secondary:hover {
            background: #cbd5e0;
            transform: scale(1.05);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }
        
        .status-label {
            font-weight: 600;
            color: #4a5568;
        }
        
        .status-value {
            font-weight: bold;
            color: #2d3748;
        }
        
        .refresh-info {
            text-align: center;
            color: white;
            margin-top: 20px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .current-temp { font-size: 3rem; }
            .controls { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 Smart Home Dashboard</h1>
            <p>Real-time control for your ${thermostatData.name} Ecobee Thermostat</p>
        </div>
        
        <div class="dashboard-grid">
            <!-- Main Temperature Display -->
            <div class="card">
                <h3>🌡️ Current Temperature</h3>
                <div class="temp-display">
                    <div class="current-temp">${thermostatData.currentTemp}°F</div>
                    <div class="temp-label">Living Room</div>
                </div>
                
                <div class="controls">
                    <a href="/command/setCoolingSetpoint/${thermostatData.coolingSetpoint + 1}" class="control-btn btn-primary">Cool +1°F</a>
                    <a href="/command/setCoolingSetpoint/${thermostatData.coolingSetpoint - 1}" class="control-btn btn-secondary">Cool -1°F</a>
                    <a href="/command/setHeatingSetpoint/${thermostatData.heatingSetpoint + 1}" class="control-btn btn-primary">Heat +1°F</a>
                    <a href="/command/setHeatingSetpoint/${thermostatData.heatingSetpoint - 1}" class="control-btn btn-secondary">Heat -1°F</a>
                </div>
            </div>
            
            <!-- System Status -->
            <div class="card">
                <h3>📊 System Status</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Mode:</span>
                        <span class="status-value">${thermostatData.mode.toUpperCase()}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">State:</span>
                        <span class="status-value">${thermostatData.operatingState.toUpperCase()}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Fan:</span>
                        <span class="status-value">${thermostatData.fanMode.toUpperCase()}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Humidity:</span>
                        <span class="status-value">${thermostatData.humidity}%</span>
                    </div>
                </div>
            </div>
            
            <!-- Setpoints -->
            <div class="card">
                <h3>🎯 Temperature Setpoints</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="status-label">Cooling:</span>
                        <span class="status-value">${thermostatData.coolingSetpoint}°F</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Heating:</span>
                        <span class="status-value">${thermostatData.heatingSetpoint}°F</span>
                    </div>
                </div>
            </div>
            
            <!-- Mode Controls -->
            <div class="card">
                <h3>🔧 Mode Controls</h3>
                <div class="controls">
                    <a href="/command/auto" class="control-btn ${thermostatData.mode === 'auto' ? 'btn-primary' : 'btn-secondary'}">Auto</a>
                    <a href="/command/heat" class="control-btn ${thermostatData.mode === 'heat' ? 'btn-primary' : 'btn-secondary'}">Heat</a>
                    <a href="/command/cool" class="control-btn ${thermostatData.mode === 'cool' ? 'btn-primary' : 'btn-secondary'}">Cool</a>
                    <a href="/command/off" class="control-btn ${thermostatData.mode === 'off' ? 'btn-primary' : 'btn-secondary'}">Off</a>
                </div>
            </div>
        </div>
        
        <div class="refresh-info">
            <p>📡 Last updated: ${thermostatData.lastUpdate}</p>
            <p><a href="/" style="color: white; text-decoration: underline;">🔄 Refresh Dashboard</a></p>
        </div>
    </div>
</body>
</html>`;
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
    const url = new URL(req.url, `http://localhost:${PORT}`);
    
    try {
        // Handle command requests
        if (url.pathname.startsWith('/command/')) {
            const pathParts = url.pathname.split('/');
            const command = pathParts[2];
            const value = pathParts[3] || '';
            
            console.log(`Executing command: ${command} ${value}`);
            
            await sendThermostatCommand(command, value);
            
            // Redirect back to dashboard after command
            res.writeHead(302, { 'Location': '/' });
            res.end();
            return;
        }
        
        // Serve dashboard
        if (url.pathname === '/') {
            const thermostatData = await getThermostatData();
            const html = generateDashboardHTML(thermostatData);
            
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(html);
            return;
        }
        
        // 404 for other paths
        res.writeHead(404);
        res.end('Not Found');
        
    } catch (error) {
        console.error('Server error:', error);
        res.writeHead(500, { 'Content-Type': 'text/html' });
        res.end(`
            <h1>Connection Error</h1>
            <p>Failed to connect to Ecobee thermostat: ${error.message}</p>
            <p><a href="/">Try Again</a></p>
        `);
    }
});

server.listen(PORT, () => {
    console.log(`✅ Smart Home Dashboard running at http://localhost:${PORT}`);
    console.log('🏠 Connected to Ecobee thermostat via Hubitat C8');
    console.log('🔄 Real-time thermostat control available');
});

server.on('error', (error) => {
    console.error('Server error:', error);
});
