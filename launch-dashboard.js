#!/usr/bin/env node

const { spawn } = require('child_process');
const http = require('http');

const DASHBOARD_URL = 'http://localhost:8083/?windowed';
const DASHBOARD_PORT = 8083;

// Check if dashboard server is running
function checkServerRunning() {
    return new Promise((resolve) => {
        const req = http.get(`http://localhost:${DASHBOARD_PORT}`, (res) => {
            resolve(true);
        });
        
        req.on('error', () => {
            resolve(false);
        });
        
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
    });
}

// Launch dashboard in separate browser instance
async function launchDashboard() {
    console.log('🏠 Smart Home Dashboard Launcher');
    console.log('================================');
    
    // Check if server is running
    console.log('📡 Checking dashboard server...');
    const serverRunning = await checkServerRunning();
    
    if (!serverRunning) {
        console.log('❌ Dashboard server not running on port 8083');
        console.log('💡 Please start the server first: node dashboard-server.js');
        process.exit(1);
    }
    
    console.log('✅ Dashboard server is running');
    console.log('🚀 Launching dashboard in separate browser...');
    
    // Try different browsers and methods
    const browsers = [
        // Chrome/Chromium with separate instance
        { 
            cmd: 'google-chrome', 
            args: ['--new-window', '--app=' + DASHBOARD_URL, '--window-size=1200,800', '--window-position=100,50']
        },
        { 
            cmd: 'chromium-browser', 
            args: ['--new-window', '--app=' + DASHBOARD_URL, '--window-size=1200,800', '--window-position=100,50']
        },
        { 
            cmd: 'chromium', 
            args: ['--new-window', '--app=' + DASHBOARD_URL, '--window-size=1200,800', '--window-position=100,50']
        },
        // Firefox with separate instance
        { 
            cmd: 'firefox', 
            args: ['--new-instance', '--new-window', DASHBOARD_URL]
        },
        // Edge
        { 
            cmd: 'microsoft-edge', 
            args: ['--new-window', '--app=' + DASHBOARD_URL]
        },
        // Safari (macOS)
        { 
            cmd: 'open', 
            args: ['-n', '-a', 'Safari', DASHBOARD_URL]
        },
        // Generic fallbacks
        { 
            cmd: 'xdg-open', 
            args: [DASHBOARD_URL]
        },
        { 
            cmd: 'open', 
            args: [DASHBOARD_URL]
        }
    ];
    
    let launched = false;
    
    for (const browser of browsers) {
        try {
            console.log(`🔍 Trying ${browser.cmd}...`);
            
            const child = spawn(browser.cmd, browser.args, {
                detached: true,
                stdio: 'ignore'
            });
            
            child.unref();
            
            // Wait a moment to see if it fails immediately
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (!child.killed) {
                console.log(`✅ Dashboard launched successfully with ${browser.cmd}`);
                console.log(`🌐 Dashboard URL: ${DASHBOARD_URL}`);
                console.log('📱 Dashboard will open in a separate browser window');
                launched = true;
                break;
            }
        } catch (error) {
            console.log(`❌ ${browser.cmd} not available`);
        }
    }
    
    if (!launched) {
        console.log('⚠️  Could not auto-launch browser');
        console.log('🌐 Please manually open: ' + DASHBOARD_URL);
    }
    
    console.log('================================');
    console.log('🏠 Smart Home Dashboard is ready!');
}

// Run the launcher
launchDashboard().catch(error => {
    console.error('❌ Error launching dashboard:', error.message);
    process.exit(1);
});
