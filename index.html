<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Home Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="dashboard">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <rect width="32" height="32" rx="8" fill="#3B82F6"/>
                        <path d="M16 8L24 16L16 24L8 16L16 8Z" fill="white"/>
                    </svg>
                    <span>SmartHome</span>
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#" class="nav-item active">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    Dashboard
                </a>
                <a href="#" class="nav-item">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                    </svg>
                    Thermostat
                </a>
                <a href="#" class="nav-item">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                    </svg>
                    Cameras
                </a>
                <a href="#" class="nav-item">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                    </svg>
                    Security
                </a>
            </div>
            
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="avatar">JD</div>
                    <div class="user-info">
                        <div class="name">John Doe</div>
                        <div class="status">Online</div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h1>Dashboard</h1>
                    <p>Welcome back, John. Here's what's happening at home.</p>
                </div>
                <div class="header-right">
                    <div class="datetime">
                        <div class="date" id="current-date"></div>
                        <div class="time" id="current-time"></div>
                    </div>
                    <button class="notification-btn">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                        </svg>
                    </button>
                </div>
            </header>

            <div class="dashboard-grid">
                <!-- Real Ecobee Stats -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon temperature">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2a5 5 0 00-5 5v6.59l-1.29 1.29A1 1 0 006 16h12a1 1 0 00.29-1.71L17 13.59V7a5 5 0 00-5-5z"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="current-temp">--°F</div>
                            <div class="stat-label">Current Temperature</div>
                            <div class="stat-change" id="temp-status">Loading...</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon humidity">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2c1 3 4 6 4 9a4 4 0 01-8 0c0-3 3-6 4-9z"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="humidity-value">--%</div>
                            <div class="stat-label">Humidity</div>
                            <div class="stat-change" id="humidity-status">Loading...</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon operating">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="operating-state">--</div>
                            <div class="stat-label">System Status</div>
                            <div class="stat-change" id="operating-status">Loading...</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon fan">
                            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" id="fan-mode">--</div>
                            <div class="stat-label">Fan Mode</div>
                            <div class="stat-change" id="fan-status">Loading...</div>
                        </div>
                    </div>
                </div>

                <!-- Thermostat Control -->
                <div class="thermostat-card">
                    <div class="card-header">
                        <h2>Ecobee Thermostat</h2>
                        <div class="connection-status" id="connection-status">
                            <div class="status-dot"></div>
                            <span>Connecting...</span>
                        </div>
                    </div>
                    
                    <div class="thermostat-content">
                        <div class="thermostat-display">
                            <div class="temperature-circle">
                                <div class="current-temp">
                                    <span class="temp-value" id="thermostat-temp">--</span>
                                    <span class="temp-unit">°F</span>
                                </div>
                                <div class="target-temp">
                                    Target: <span id="target-temp">--</span>°F
                                </div>
                            </div>
                            
                            <div class="temp-controls">
                                <button class="temp-btn" id="temp-down">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                                <button class="temp-btn" id="temp-up">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="thermostat-controls">
                            <div class="control-group">
                                <label>Thermostat Mode</label>
                                <div class="mode-buttons">
                                    <button class="mode-btn" data-mode="auto">Auto</button>
                                    <button class="mode-btn" data-mode="heat">Heat</button>
                                    <button class="mode-btn" data-mode="cool">Cool</button>
                                    <button class="mode-btn" data-mode="emergencyHeat">Emergency</button>
                                    <button class="mode-btn" data-mode="off">Off</button>
                                </div>
                            </div>

                            <div class="control-group">
                                <label>Fan Control</label>
                                <div class="fan-buttons">
                                    <button class="fan-btn" data-fan="auto">Auto</button>
                                    <button class="fan-btn" data-fan="on">On</button>
                                    <button class="fan-btn" data-fan="circulate">Circulate</button>
                                </div>
                            </div>

                            <div class="control-group">
                                <label>Temperature Setpoints</label>
                                <div class="setpoint-controls">
                                    <div class="setpoint-item">
                                        <span class="setpoint-label">Heating:</span>
                                        <div class="setpoint-control">
                                            <button class="setpoint-btn" id="heat-down">-</button>
                                            <span class="setpoint-value" id="heating-setpoint">68°F</span>
                                            <button class="setpoint-btn" id="heat-up">+</button>
                                        </div>
                                    </div>
                                    <div class="setpoint-item">
                                        <span class="setpoint-label">Cooling:</span>
                                        <div class="setpoint-control">
                                            <button class="setpoint-btn" id="cool-down">-</button>
                                            <span class="setpoint-value" id="cooling-setpoint">77°F</span>
                                            <button class="setpoint-btn" id="cool-up">+</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="quick-actions">
                                <button class="action-btn" id="away-mode">Set Away</button>
                                <button class="action-btn" id="resume-program">Resume Program</button>
                                <button class="action-btn" id="refresh-data">Refresh Data</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed System Info -->
                <div class="system-card">
                    <div class="card-header">
                        <h3>Ecobee System Details</h3>
                    </div>
                    <div class="system-info">
                        <div class="info-section">
                            <h4>Connection Status</h4>
                            <div class="info-item">
                                <span class="label">API Status:</span>
                                <span class="value" id="api-status">Checking...</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Device Status:</span>
                                <span class="value" id="device-status">--</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Last Update:</span>
                                <span class="value" id="last-update">--</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h4>Temperature Limits</h4>
                            <div class="info-item">
                                <span class="label">Min Heating:</span>
                                <span class="value" id="min-heating">--°F</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Max Heating:</span>
                                <span class="value" id="max-heating">--°F</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Min Cooling:</span>
                                <span class="value" id="min-cooling">--°F</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Max Cooling:</span>
                                <span class="value" id="max-cooling">--°F</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h4>Capabilities</h4>
                            <div class="info-item">
                                <span class="label">Supported Modes:</span>
                                <span class="value" id="supported-modes">--</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Fan Modes:</span>
                                <span class="value" id="supported-fan-modes">--</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Temperature Unit:</span>
                                <span class="value" id="temp-unit">--</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
