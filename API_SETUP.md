# Smart Home Dashboard - API Integration Guide

## Overview
This dashboard can connect to your real Ecobee thermostat through multiple integration methods. Since Ecobee is not accepting new developer accounts, we provide alternative approaches.

## Integration Options

### 1. Home Assistant Integration (Recommended)

Home Assistant is the most reliable way to control your Ecobee thermostat.

#### Setup Steps:
1. **Install Home Assistant** on your network (if not already installed)
   - Raspberry Pi, Docker, or dedicated hardware
   - Access at `http://*************:8123` (update IP as needed)

2. **Add Ecobee Integration**
   - Go to Settings → Devices & Services
   - Add Integration → Search "Ecobee"
   - Follow authentication flow with your Ecobee account

3. **Get Long-Lived Access Token**
   - Go to Profile → Security → Long-Lived Access Tokens
   - Create new token, copy it

4. **Update Dashboard Configuration**
   ```javascript
   // In script.js, update the config object:
   this.config = {
       homeAssistantUrl: 'http://*************:8123',
       homeAssistantToken: 'YOUR_LONG_LIVED_TOKEN_HERE',
       // ... other settings
   };
   ```

### 2. Hubitat Integration

Your Hubitat C8 hub can control Ecobee thermostats.

#### Setup Steps:
1. **Install Ecobee App on Hubitat**
   - Go to Apps → Add Built-In App
   - Search for "Ecobee" or install community driver

2. **Configure API Access**
   - Enable Maker API in Hubitat
   - Note your Access Token and App ID

3. **Update Dashboard Configuration**
   ```javascript
   this.config = {
       hubitatUrl: 'http://*************',
       hubitatToken: 'YOUR_HUBITAT_TOKEN',
       // ... other settings
   };
   ```

### 3. Direct Ecobee API (If You Have Access)

If you already have Ecobee developer access:

1. **Get API Credentials**
   - Client ID and Authorization Code from Ecobee Developer Portal

2. **Update Configuration**
   ```javascript
   this.config = {
       ecobeeApiUrl: 'https://api.ecobee.com/1',
       ecobeeToken: 'YOUR_ECOBEE_ACCESS_TOKEN',
       // ... other settings
   };
   ```

## API Endpoints

### Home Assistant Endpoints
- **Get Thermostat State**: `GET /api/states/climate.ecobee_thermostat`
- **Set Temperature**: `POST /api/services/climate/set_temperature`
- **Set Mode**: `POST /api/services/climate/set_hvac_mode`

### Hubitat Endpoints
- **Device List**: `GET /device/list`
- **Device Status**: `GET /device/{id}`
- **Control Commands**: `POST /apps/api/{app_id}/devices/{device_id}/{command}`

## Real Data Integration

Once configured, the dashboard will:

1. **Auto-discover** your thermostat through available APIs
2. **Display real-time** temperature and status
3. **Send actual commands** to your Ecobee thermostat
4. **Update automatically** every 30 seconds

## Testing Your Setup

1. **Open Browser Console** (F12)
2. **Look for connection messages**:
   - "Home Assistant connection successful"
   - "Hubitat connection successful"
   - "Found thermostats: [...]"

3. **Test Controls**:
   - Adjust temperature with +/- buttons
   - Change modes (Auto, Heat, Cool)
   - Try quick actions (Away, Sleep)

## Troubleshooting

### Common Issues:

1. **CORS Errors**
   - Add your dashboard domain to Home Assistant's `http` configuration
   - Use `cors_allowed_origins: ["*"]` for testing

2. **Authentication Failures**
   - Verify your access tokens are correct
   - Check token permissions and expiration

3. **Network Issues**
   - Ensure all devices are on the same network
   - Check firewall settings

### Demo Mode
If no real connections are available, the dashboard runs in demo mode with:
- Simulated temperature changes
- Functional controls (local only)
- Realistic data patterns

## Security Notes

- **Never commit API tokens** to version control
- **Use environment variables** for production deployment
- **Enable HTTPS** for production use
- **Restrict API access** to your local network

## Production Deployment

For Azure deployment:
1. Use Azure App Service or Static Web Apps
2. Store API credentials in Azure Key Vault
3. Configure CORS policies properly
4. Enable SSL/TLS encryption

## Support

For issues with:
- **Home Assistant**: Check Home Assistant community forums
- **Hubitat**: Consult Hubitat documentation
- **Ecobee API**: Refer to Ecobee developer docs (if accessible)

The dashboard is designed to be professional and production-ready with proper API integration.
