@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-gray-50 font-sans;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 transition-all duration-300 hover:shadow-md;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-lg transition-all duration-200 hover:translate-x-1;
  }

  .sidebar-item.active {
    @apply bg-primary-50 text-primary-700 border-r-2 border-primary-500;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
  }

  .temperature-control {
    @apply transition-all duration-300 hover:scale-105;
  }

  .mode-button {
    @apply transition-all duration-200 hover:scale-105 active:scale-95;
  }

  .stat-card {
    @apply transform transition-all duration-300 hover:scale-105 hover:shadow-lg;
  }
}
