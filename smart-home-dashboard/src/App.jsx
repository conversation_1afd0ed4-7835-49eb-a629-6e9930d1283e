import React from 'react';
import DashboardLayout from './components/layout/DashboardLayout';
import DashboardHeader from './components/layout/DashboardHeader';
import StatsOverview from './components/layout/StatsOverview';
import ThermostatWidget from './components/widgets/ThermostatWidget';

function App() {
  return (
    <DashboardLayout>
      <DashboardHeader />
      <StatsOverview />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Thermostat Control - Main widget */}
        <div className="lg:col-span-2">
          <ThermostatWidget />
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="space-y-3">
            <button className="btn-primary w-full">
              Set Away Mode
            </button>
            <button className="btn-secondary w-full">
              Schedule Override
            </button>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recent Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center text-sm">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span className="text-gray-600">Temperature set to 72°F</span>
            </div>
            <div className="flex items-center text-sm">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span className="text-gray-600">Schedule updated</span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

export default App;
