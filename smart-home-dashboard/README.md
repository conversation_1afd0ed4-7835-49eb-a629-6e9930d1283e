# Smart Home Dashboard

A modern, responsive dashboard for controlling smart home devices, starting with Ecobee thermostat control. Built with React, Vite, and Tailwind CSS, inspired by the CRMi Admin dashboard design.

## Features

- **Modern Dashboard Layout**: Clean, professional interface with sidebar navigation
- **Ecobee Thermostat Control**: Full thermostat control with temperature adjustment, mode selection, and scheduling
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Smooth Animations**: Polished user experience with hover effects and transitions
- **Real-time Stats**: Overview cards showing system status and metrics

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

## Project Structure

```
src/
├── components/
│   ├── layout/
│   │   ├── DashboardLayout.jsx    # Main layout with sidebar
│   │   ├── DashboardHeader.jsx    # Header with welcome message
│   │   └── StatsOverview.jsx      # Statistics cards
│   └── widgets/
│       └── ThermostatWidget.jsx   # Ecobee thermostat control
├── App.jsx                        # Main application component
├── main.jsx                       # Application entry point
└── index.css                      # Global styles with Tailwind
```

## Technologies Used

- **React 18**: Modern React with hooks
- **Vite**: Fast build tool and dev server
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Beautiful icon library
- **PostCSS**: CSS processing

## Future Enhancements

- Integration with actual Ecobee API
- Camera feed widgets
- Smart lock controls
- Energy usage charts
- Mobile app companion
- Voice control integration

## License

MIT License
